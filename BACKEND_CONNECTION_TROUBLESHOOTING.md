# Backend Connection Troubleshooting Guide

## Problem: React Native App Cannot Connect to Django Backend

### Current Issue
Your React Native app is trying to connect to `http://********:8000` but getting timeout errors (`AbortError`). The Django server is running on `http://127.0.0.1:8000` but is not accessible from the Android emulator.

## Root Cause
The Django development server is bound to `127.0.0.1` (localhost only) instead of `0.0.0.0` (all interfaces), making it inaccessible from the Android emulator.

## Solutions

### Solution 1: Start Django Server on All Interfaces (Recommended)

1. **Stop the current Django server** (if running)
2. **Start the server with the correct binding:**

   ```bash
   cd backend
   python manage.py runserver 0.0.0.0:8000
   ```

   Or use the provided scripts:
   - **Windows**: Double-click `backend/start_server.bat`
   - **Cross-platform**: Run `python backend/start_server.py`

3. **Verify the server is accessible:**
   - Local: http://127.0.0.1:8000
   - Android Emulator: http://********:8000
   - Network: http://[your-ip]:8000

### Solution 2: Update React Native Configuration

If you cannot change how the Django server runs, update the React Native app configuration:

1. **Edit `Estate_link_App/src/utils/networkUtils.ts`**
2. **Change the `getBaseUrl()` function:**

   ```typescript
   const getBaseUrl = () => {
     // For development when Django runs on 127.0.0.1:8000
     return 'http://127.0.0.1:8000';
     
     // Or for iOS Simulator:
     // return 'http://localhost:8000';
   };
   ```

### Solution 3: Use Your Computer's IP Address

1. **Find your computer's IP address:**
   ```bash
   ipconfig  # Windows
   ifconfig  # Mac/Linux
   ```

2. **Start Django server on all interfaces:**
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

3. **Update React Native configuration:**
   ```typescript
   const getBaseUrl = () => {
     return 'http://[YOUR_IP_ADDRESS]:8000';  // e.g., 'http://*************:8000'
   };
   ```

## Testing Your Connection

### Method 1: Use the Built-in Diagnostics
In your React Native app, the connection test functions will automatically run and provide detailed diagnostics.

### Method 2: Manual Testing
Test the connection manually from your development machine:

```bash
# Test if server is running locally
curl http://127.0.0.1:8000

# Test if server is accessible from network
curl http://********:8000  # This should work after fixing

# Test specific API endpoint
curl -X POST http://********:8000/user/check_status/ \
  -H "Content-Type: application/json" \
  -d '{"authenticator": "<EMAIL>"}'
```

## Common Issues and Fixes

### Issue 1: Port Already in Use
**Error**: `Port 8000 is already in use`
**Solution**: 
- Find and stop the existing process: `netstat -ano | findstr :8000`
- Or use a different port: `python manage.py runserver 0.0.0.0:8001`

### Issue 2: Firewall Blocking Connection
**Solution**: 
- Allow Python/Django through Windows Firewall
- Temporarily disable firewall for testing

### Issue 3: Network Configuration Issues
**Solution**:
- Ensure your computer and emulator are on the same network
- Check if VPN is interfering with local connections
- Try using your computer's actual IP address instead of ********

### Issue 4: Django Settings Issues
**Solution**:
- Verify `ALLOWED_HOSTS` in `backend/backend/settings.py` includes:
  ```python
  ALLOWED_HOSTS = ['127.0.0.1', 'localhost', '********', '[your-ip]']
  ```

## Verification Steps

1. **Django server starts successfully** ✓
2. **Server accessible at http://127.0.0.1:8000** ✓
3. **Server accessible at http://********:8000** (should work after fix)
4. **React Native app connects successfully** (should work after fix)
5. **API endpoints respond correctly** (should work after fix)

## Quick Fix Commands

```bash
# Navigate to backend directory
cd backend

# Start server on all interfaces
python manage.py runserver 0.0.0.0:8000

# In another terminal, test the connection
curl http://********:8000
```

## Files Modified
- `Estate_link_App/src/utils/networkUtils.ts` - Updated comments and configuration
- `Estate_link_App/src/utils/testBackendConnection.ts` - Enhanced error handling and diagnostics
- `backend/start_server.py` - New script to start server correctly
- `backend/start_server.bat` - Windows batch file for easy server startup

## Next Steps
1. Stop your current Django server
2. Start it using one of the provided methods
3. Test the connection from your React Native app
4. The timeout errors should be resolved

If you continue to experience issues, run the comprehensive diagnostics function in your React Native app for detailed troubleshooting information.
