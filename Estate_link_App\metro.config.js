const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');
const path = require('path');

const config = getDefaultConfig(__dirname);

// Add resolver alias for path mappings
config.resolver.alias = {
  components: path.resolve(__dirname, 'components'),
  'store': path.resolve(__dirname, 'src/store'),
  'hooks': path.resolve(__dirname, 'src/hooks'),
  'validation': path.resolve(__dirname, 'src/validation'),
  'types': path.resolve(__dirname, 'src/types'),
};

module.exports = withNativeWind(config, { input: './global.css' });
