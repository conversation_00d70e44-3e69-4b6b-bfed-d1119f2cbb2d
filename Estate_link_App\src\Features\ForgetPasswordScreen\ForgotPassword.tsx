import { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Image, StatusBar, Keyboard, TouchableWithoutFeedback, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage } from 'components';
import Ionicons from '@expo/vector-icons/Ionicons';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { requestOTP, clearError } from 'store/slices/authSlice';
import { useFormValidation } from 'hooks/useFormValidation';
import { forgotPasswordSchema } from 'validation/schemas';

type RootStackParamList = {
  Login: undefined;
  ForgotPassword: undefined;
  PasswordReset: undefined;
  VerifyCode: undefined;
};

type ForgotPasswordScreenNavigationProp = StackNavigationProp<RootStackParamList, 'ForgotPassword'>;

type RecoveryMethod = 'email' | 'phone' | 'whatsapp';

export function ForgotPassword() {
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [selectedMethod, setSelectedMethod] = useState<RecoveryMethod>('email');
  const [contactInfo, setContactInfo] = useState('');

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(forgotPasswordSchema);

  const handleInputChange = async (value: string) => {
    setContactInfo(value);
    
    // Clear Redux error when user starts typing
    if (error) {
      dispatch(clearError());
    }

    // Validate field on change
    if (isFieldTouched('contactInfo')) {
      const fieldError = await validateForm({ 
        contactInfo: value, 
        method: selectedMethod 
      });
      if (fieldError.isValid) {
        // Clear field error if validation passes
      }
    }
  };

  const handleMethodChange = (method: RecoveryMethod) => {
    setSelectedMethod(method);
    // Clear errors when method changes
    clearErrors();
    dispatch(clearError());
  };

  const handleBlur = () => {
    setFieldTouched('contactInfo', true);
  };

  const handleSubmit = async () => {
    // Clear previous errors
    clearErrors();
    dispatch(clearError());

    // Validate form
    const validation = await validateForm({
      contactInfo,
      method: selectedMethod,
    });

    if (!validation.isValid) {
      return;
    }

    try {
      // Request OTP
      await dispatch(requestOTP({ 
        email: contactInfo, 
        method: selectedMethod 
      })).unwrap();
      
      // Navigate to verification code screen
      navigation.navigate('VerifyCode');
    } catch (error) {
      // Error handling is done in Redux slice
      console.error('OTP request error:', error);
    }
  };

  const getPlaceholderText = () => {
    switch (selectedMethod) {
      case 'email':
        return 'Enter your email address';
      case 'phone':
        return 'Enter your phone number';
      case 'whatsapp':
        return 'Enter your WhatsApp number';
      default:
        return 'Enter your contact information';
    }
  };

  const getKeyboardType = () => {
    switch (selectedMethod) {
      case 'email':
        return 'email-address';
      case 'phone':
      case 'whatsapp':
        return 'phone-pad';
      default:
        return 'default';
    }
  };

  const contactInfoError = getFieldError('contactInfo');
  const showContactInfoError = isFieldTouched('contactInfo') && contactInfoError;
  const isFormValid = contactInfo.trim() && !contactInfoError;

  const RadioButton = ({
    selected,
    onPress,
    label,
  }: {
    selected: boolean;
    onPress: () => void;
    label: string;
  }) => (
    <TouchableOpacity className="mr-6 flex-row items-center" onPress={onPress} disabled={isLoading}>
      <View
        className="items-center justify-center"
        style={{
          width: 20,
          height: 20,
          borderRadius: 10,
          borderWidth: 2,
          borderColor: '#3C9D9B',
          marginRight: 8,
          backgroundColor: 'transparent',
        }}>
        {selected && (
          <View
            style={{
              width: 10,
              height: 10,
              borderRadius: 5,
              backgroundColor: '#3C9D9B',
            }}
          />
        )}
      </View>
      <Text
        className="font-oxanium-medium text-text-primary"
        style={{ fontSize: 16, fontWeight: '400' }}>
        {label}
      </Text>
    </TouchableOpacity>
  );

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Header with Back Button */}
        <View className="flex-row items-center px-6 pb-4 pt-20">
          <TouchableOpacity className="flex-row items-center" onPress={() => navigation.goBack()} disabled={isLoading}>
            <Ionicons name="chevron-back" size={18} color="#6B7280" style={{ marginRight: 8 }} />
            <Text className="font-oxanium-medium text-text-secondary" style={{ fontSize: 18 }}>
              Back to login
            </Text>
          </TouchableOpacity>
        </View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-8">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 60 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 40 }}
              resizeMode="contain"
            />
          </View>

          {/* Title and Description */}
          <View style={{ marginBottom: 40 }}>
            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{ fontSize: 40, fontWeight: '600', marginBottom: 20, lineHeight: 40 }}>
              Forgot your password?
            </Text>

            <Text
              className="text-center font-oxanium-medium text-text-secondary"
              style={{ fontSize: 18, fontWeight: '400', lineHeight: 20, paddingHorizontal: 20 }}>
              Don&apos;t worry, happens to all of us. Enter your email or phone number below to
              recover your password
            </Text>
          </View>

          {/* Radio Button Options */}
          <View className="flex-row justify-center" style={{ marginBottom: 32 }}>
            <RadioButton
              selected={selectedMethod === 'email'}
              onPress={() => handleMethodChange('email')}
              label="Email"
            />
            <RadioButton
              selected={selectedMethod === 'phone'}
              onPress={() => handleMethodChange('phone')}
              label="Phone Number"
            />
            <RadioButton
              selected={selectedMethod === 'whatsapp'}
              onPress={() => handleMethodChange('whatsapp')}
              label="WhatsApp"
            />
          </View>

          {/* Input Field */}
          <View style={{ marginBottom: 32 }}>
            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showContactInfoError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 56,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 16,
                marginBottom: showContactInfoError ? 8 : 0,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder={getPlaceholderText()}
              placeholderTextColor="#9CA3AF"
              value={contactInfo}
              onChangeText={handleInputChange}
              onBlur={handleBlur}
              keyboardType={getKeyboardType()}
              returnKeyType="done"
              onSubmitEditing={Keyboard.dismiss}
              blurOnSubmit={true}
              editable={!isLoading}
            />

            {showContactInfoError && (
              <ErrorMessage message={contactInfoError} visible={true} />
            )}

            {/* Redux Error Message */}
            {error && (
              <ErrorMessage message={error} visible={true} />
            )}
          </View>

          {/* Submit Button */}
          <TouchableOpacity
            className="items-center justify-center"
            style={{
              height: 56,
              backgroundColor: isFormValid && !isLoading ? '#3C9D9B' : 'white',
              borderWidth: 2,
              borderColor: '#3C9D9B',
              borderRadius: 28,
              marginBottom: 40,
            }}
            onPress={handleSubmit}
            disabled={!isFormValid || isLoading}>
            {isLoading ? (
              <ActivityIndicator color="#3C9D9B" size="small" />
            ) : (
              <Text
                className="font-oxanium-bold"
                style={{
                  fontSize: 18,
                  fontWeight: '600',
                  color: isFormValid ? 'white' : '#3C9D9B',
                }}>
                Submit
              </Text>
            )}
          </TouchableOpacity>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity disabled={isLoading}>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
