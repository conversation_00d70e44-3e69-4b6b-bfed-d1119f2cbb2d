import { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StatusBar,
  Keyboard,
  TouchableWithoutFeedback,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { ErrorMessage } from 'components';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { loginUser, checkUserStatus, clearError } from 'store/slices/authSlice';
import { useFormValidation } from 'hooks/useFormValidation';
import { loginSchema } from 'validation/schemas';
import { isNetworkError } from '../../utils/networkUtils';
import { testBackendConnection, logNetworkInfo } from '../../utils/testBackendConnection';

type RootStackParamList = {
  Login: undefined;
  WelcomeBack: undefined;
  PasswordReset: undefined;
  ForgotPassword: undefined;
};

type LoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Login'>;

export function Login() {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const dispatch = useAppDispatch();
  const { isLoading, error, user } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState({
    username: '',
    rememberMe: false,
  });

  const {
    errors,
    validateForm,
    setFieldTouched,
    getFieldError,
    isFieldTouched,
    clearErrors,
  } = useFormValidation(loginSchema);

  // Test backend connection on component mount
  useEffect(() => {
    logNetworkInfo();
    testBackendConnection().then(result => {
      console.log('Backend connection test result:', result);
    });
  }, []);

  const handleInputChange = async (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    
    // Clear Redux error when user starts typing
    if (error) {
      dispatch(clearError());
    }

    // Validate field on change
    if (isFieldTouched(field as keyof typeof formData)) {
      const fieldError = await validateForm({ ...formData, [field]: value });
      if (fieldError.isValid) {
        // Clear field error if validation passes
      }
    }
  };

  const handleBlur = (field: string) => {
    setFieldTouched(field as keyof typeof formData, true);
  };

  const handleLogin = async () => {
    // Clear previous errors
    clearErrors();
    dispatch(clearError());

    // Validate form
    const validation = await validateForm(formData);
    if (!validation.isValid) {
      return;
    }

    try {
      console.log('Form Data:', formData);
      console.log('Calling checkUserStatus API...');
      
      // Check user status first
      const statusResult = await dispatch(checkUserStatus(formData.username)).unwrap();
      
      if (statusResult.is_first_login) {
        // Navigate to initial password reset for first-time users
        navigation.navigate('PasswordReset');
      } else {
        // Navigate to welcome back for existing users
        navigation.navigate('WelcomeBack');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      
      // Log detailed error information for debugging
      console.log('Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      });
      
      // If it's a network error, provide a fallback option
      if (isNetworkError(error)) {
        console.log('Navigating to WelcomeBack as fallback due to API error');
        // Navigate to WelcomeBack as fallback for network issues
        navigation.navigate('WelcomeBack');
      }
      // For other errors, let Redux handle the error display
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const usernameError = getFieldError('username');
  const showUsernameError = isFieldTouched('username') && usernameError;
  const isFormValid = formData.username.trim() && !usernameError;

  return (
    <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
      <View className="flex-1 bg-white">
        <StatusBar barStyle="dark-content" backgroundColor="white" />

        {/* Status Bar Area */}
        <View className="h-11 flex-row items-center justify-between px-6 pb-2 pt-32"></View>

        {/* Main Container */}
        <View className="flex-1 px-6 pt-10">
          {/* Logo Section */}
          <View className="items-center" style={{ marginBottom: 80 }}>
            <Image
              source={require('../../../assets/Logo.png')}
              style={{ width: 200, height: 64, marginBottom: 50 }}
              resizeMode="contain"
            />

            <Text
              className="text-center font-oxanium-bold text-text-primary"
              style={{
                fontSize: 30,
                fontWeight: '500',
                marginBottom: 8,
                lineHeight: 30,
                letterSpacing: 0.3,
              }}>
              Welcome Back
            </Text>

            <Text
              className="text-center font-oxanium text-text-secondary"
              style={{ fontSize: 16, fontWeight: '500' }}>
              Login to access your Estate Link account
            </Text>
          </View>

          {/* Form Section */}
          <View>
            <Text
              className="font-oxanium-bold text-text-primary"
              style={{ fontSize: 16, fontWeight: '500', marginBottom: 8 }}>
              User Name / Email / Phone Number
            </Text>

            <TextInput
              className={`rounded-lg border bg-background-input text-text-primary ${
                showUsernameError ? 'border-red-500' : 'border-border'
              }`}
              style={{
                height: 48,
                borderWidth: 1,
                paddingHorizontal: 16,
                fontSize: 14,
                marginBottom: showUsernameError ? 8 : 24,
                fontFamily: 'Oxanium-Medium',
              }}
              placeholder="enter your user name / e-mail / phone number"
              placeholderTextColor="#9CA3AF"
              value={formData.username}
              onChangeText={(value) => handleInputChange('username', value)}
              onBlur={() => handleBlur('username')}
              returnKeyType="next"
              onSubmitEditing={handleLogin}
              blurOnSubmit={true}
              autoCapitalize="none"
              autoCorrect={false}
              editable={!isLoading}
            />

            {showUsernameError && (
              <ErrorMessage message={usernameError} visible={true} />
            )}

            {/* Redux Error Message */}
            {error && (
              <ErrorMessage message={error} visible={true} />
            )}

            {/* Remember Me Checkbox */}
            <TouchableOpacity
              className="flex-row items-center"
              style={{ marginBottom: 32, paddingVertical: 8 }}
              onPress={() => setFormData((prev) => ({ ...prev, rememberMe: !prev.rememberMe }))}
              activeOpacity={0.7}
              disabled={isLoading}>
              <View
                className="items-center justify-center"
                style={{
                  width: 18,
                  height: 18,
                  borderWidth: 2,
                  borderColor: '#3C9D9B',
                  borderRadius: 4,
                  marginRight: 12,
                  backgroundColor: formData.rememberMe ? '#3C9D9B' : 'transparent',
                }}>
                {formData.rememberMe && (
                  <Text className="font-bold text-white" style={{ fontSize: 12 }}>
                    ✓
                  </Text>
                )}
              </View>
              <Text
                className="font-oxanium-bold text-text-primary"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Remember me
              </Text>
            </TouchableOpacity>

            {/* Login Button */}
            <TouchableOpacity
              className="items-center justify-center"
              style={{
                width: '100%',
                height: 48,
                backgroundColor: isFormValid && !isLoading ? '#3C9D9B' : 'white',
                borderWidth: 1.5,
                borderColor: '#3C9D9B',
                borderRadius: 24,
                marginBottom: 20,
              }}
              onPress={handleLogin}
              disabled={!isFormValid || isLoading}>
              {isLoading ? (
                <ActivityIndicator color="#3C9D9B" size="small" />
              ) : (
                <Text
                  className="font-oxanium-bold"
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: isFormValid ? 'white' : '#3C9D9B',
                  }}>
                  Login
                </Text>
              )}
            </TouchableOpacity>

            {/* Forgot Password Link */}
            <View className="items-center" style={{ marginBottom: 60 }}>
              <TouchableOpacity onPress={handleForgotPassword} disabled={isLoading}>
                <Text
                  className="font-oxanium-bold text-primary"
                  style={{ fontSize: 16, fontWeight: '600' }}>
                  Forgot Password?
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Bottom Link */}
          <View className="flex-1 items-center justify-end" style={{ paddingBottom: 40 }}>
            <TouchableOpacity disabled={isLoading}>
              <Text
                className="font-oxanium-bold text-text-primary underline"
                style={{ fontSize: 16, fontWeight: '400' }}>
                Log into Estate Control
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
}
