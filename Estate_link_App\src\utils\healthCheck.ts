import { API_CONFIG } from './networkUtils';

export interface HealthCheckResult {
  isHealthy: boolean;
  message: string;
  responseTime?: number;
  details?: any;
}

export const performHealthCheck = async (): Promise<HealthCheckResult> => {
  const startTime = Date.now();
  
  try {
    console.log('Performing health check on:', API_CONFIG.BASE_URL);
    
    // Simple fetch to check if server is responding
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
    
    const response = await fetch(`${API_CONFIG.BASE_URL}/user/check_status/`, {
      method: 'HEAD',
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
      },
    });
    
    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;
    
    console.log(`Health check completed in ${responseTime}ms with status: ${response.status}`);
    
    return {
      isHealthy: true,
      message: `Server is responding (${response.status})`,
      responseTime,
      details: {
        status: response.status,
        statusText: response.statusText,
      }
    };
    
  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    
    console.error('Health check failed:', error);
    
    let message = 'Server is not responding';
    if (error.name === 'AbortError') {
      message = 'Server response timeout (>10s)';
    } else if (error.message?.includes('Network request failed')) {
      message = 'Network connection failed';
    } else if (error.message?.includes('fetch')) {
      message = 'Unable to reach server';
    }
    
    return {
      isHealthy: false,
      message,
      responseTime,
      details: {
        error: error.message,
        name: error.name,
      }
    };
  }
};

export const logHealthCheckResult = (result: HealthCheckResult) => {
  console.log('=== Health Check Result ===');
  console.log('Healthy:', result.isHealthy);
  console.log('Message:', result.message);
  console.log('Response Time:', result.responseTime, 'ms');
  if (result.details) {
    console.log('Details:', result.details);
  }
  console.log('==========================');
};
