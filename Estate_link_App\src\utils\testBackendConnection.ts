import { API_CONFIG, enhancedFetch } from './networkUtils';

export const testBackendConnection = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log('Testing backend connection...');
    console.log('Backend URL:', API_CONFIG.BASE_URL);

    // First test with a simple HEAD request to check if server is reachable
    console.log('Testing server reachability...');
    const headResponse = await enhancedFetch(
      `${API_CONFIG.BASE_URL}/user/check_status/`,
      {
        method: 'HEAD',
      },
      10000 // 10 second timeout for testing
    );

    console.log('Server is reachable, testing API endpoint...');

    // Test actual API endpoint
    const response = await enhancedFetch(
      `${API_CONFIG.BASE_URL}/user/check_status/`,
      {
        method: 'POST',
        body: JSON.stringify({ authenticator: '<EMAIL>' }),
      },
      10000 // 10 second timeout for testing
    );

    console.log('Backend connection test successful');
    console.log('Response status:', response.status);

    return {
      success: true,
      message: 'Backend connection successful',
      details: {
        status: response.status,
        statusText: response.statusText,
        headStatus: headResponse.status,
      }
    };
  } catch (error: any) {
    console.error('Backend connection test failed:', error);

    // Provide more specific error messages based on error type
    let message = 'Backend connection failed';
    let troubleshooting = '';

    if (error.name === 'AbortError' || error.message === 'Aborted') {
      message = 'Connection timeout - Server not responding';
      troubleshooting = 'Make sure Django server is running on 0.0.0.0:8000';
    } else if (error.message?.includes('Network request failed')) {
      message = 'Network connection failed';
      troubleshooting = 'Check your internet connection and server status';
    } else if (error.message?.includes('fetch')) {
      message = 'Unable to reach server';
      troubleshooting = 'Verify server is running and accessible';
    }

    return {
      success: false,
      message,
      details: {
        error: error.message,
        name: error.name,
        stack: error.stack,
        troubleshooting,
        serverUrl: API_CONFIG.BASE_URL,
      }
    };
  }
};

export const logNetworkInfo = () => {
  console.log('=== Network Configuration ===');
  console.log('API Base URL:', API_CONFIG.BASE_URL);
  console.log('Timeout:', API_CONFIG.TIMEOUT, 'ms');
  console.log('Retry Attempts:', API_CONFIG.RETRY_ATTEMPTS);
  console.log('Retry Delay:', API_CONFIG.RETRY_DELAY, 'ms');
  console.log('============================');
};

export const runComprehensiveDiagnostics = async (): Promise<{
  overallStatus: 'healthy' | 'issues' | 'failed';
  results: Array<{
    test: string;
    status: 'pass' | 'fail' | 'warning';
    message: string;
    details?: any;
  }>;
}> => {
  const results = [];
  let overallStatus: 'healthy' | 'issues' | 'failed' = 'healthy';

  console.log('=== Running Comprehensive Backend Diagnostics ===');

  // Test 1: Basic connectivity to main URL
  try {
    const response = await fetch(API_CONFIG.BASE_URL, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    });
    results.push({
      test: 'Basic Server Connectivity',
      status: 'pass' as const,
      message: `Server responding with status ${response.status}`,
      details: { status: response.status, statusText: response.statusText }
    });
  } catch (error: any) {
    results.push({
      test: 'Basic Server Connectivity',
      status: 'fail' as const,
      message: `Cannot reach server: ${error.message}`,
      details: { error: error.name, message: error.message }
    });
    overallStatus = 'failed';
  }

  // Test 2: API endpoint availability
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/user/check_status/`, {
      method: 'HEAD',
      signal: AbortSignal.timeout(5000)
    });
    results.push({
      test: 'API Endpoint Availability',
      status: 'pass' as const,
      message: `API endpoint responding with status ${response.status}`,
      details: { status: response.status }
    });
  } catch (error: any) {
    results.push({
      test: 'API Endpoint Availability',
      status: 'fail' as const,
      message: `API endpoint not accessible: ${error.message}`,
      details: { error: error.name }
    });
    if (overallStatus !== 'failed') overallStatus = 'issues';
  }

  // Test 3: Alternative URLs (for troubleshooting)
  const alternativeUrls = [
    'http://localhost:8000',
    'http://127.0.0.1:8000',
    'http://*************:8000'
  ];

  for (const url of alternativeUrls) {
    if (url === API_CONFIG.BASE_URL) continue; // Skip if it's the same as current config

    try {
      const response = await fetch(url, {
        method: 'HEAD',
        signal: AbortSignal.timeout(3000)
      });
      results.push({
        test: `Alternative URL Test (${url})`,
        status: 'warning' as const,
        message: `Server accessible at ${url} (status ${response.status})`,
        details: {
          suggestion: `Consider updating API_CONFIG.BASE_URL to ${url}`,
          status: response.status
        }
      });
    } catch (error: any) {
      results.push({
        test: `Alternative URL Test (${url})`,
        status: 'fail' as const,
        message: `Not accessible at ${url}`,
        details: { error: error.name }
      });
    }
  }

  console.log('=== Diagnostics Complete ===');

  return { overallStatus, results };
};