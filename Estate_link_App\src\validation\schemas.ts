import * as yup from 'yup';

// Login validation schema
export const loginSchema = yup.object().shape({
  username: yup
    .string()
    .required('Username, email, or phone number is required')
    .min(3, 'Must be at least 3 characters')
    .max(50, 'Must be less than 50 characters'),
  rememberMe: yup.boolean().default(false),
});

// Forgot password validation schema
export const forgotPasswordSchema = yup.object().shape({
  contactInfo: yup
    .string()
    .required('Contact information is required')
    .test('email-validation', 'Please enter a valid email address', function (value) {
      const { method } = this.parent;
      if (method === 'email') {
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(value || '');
      }
      return true;
    })
    .test('phone-validation', 'Please enter a valid phone number', function (value) {
      const { method } = this.parent;
      if (method === 'phone' || method === 'whatsapp') {
        const cleanedPhone = (value || '').replace(/[^\d+]/g, '');
        if (cleanedPhone.startsWith('+')) {
          return /^\+[1-9]\d{6,14}$/.test(cleanedPhone);
        } else {
          return /^[1-9]\d{6,14}$/.test(cleanedPhone);
        }
      }
      return true;
    }),
  method: yup
    .string()
    .oneOf(['email', 'phone', 'whatsapp'], 'Invalid recovery method')
    .required('Recovery method is required'),
});

// OTP verification schema
export const otpSchema = yup.object().shape({
  otp: yup
    .string()
    .required('OTP is required')
    .matches(/^\d{4,6}$/, 'OTP must be 4-6 digits')
    .length(4, 'OTP must be exactly 4 digits'),
});

// Password validation schema
export const passwordSchema = yup.object().shape({
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('password')], 'Passwords must match'),
});

// Set password schema (for first-time users)
export const setPasswordSchema = yup.object().shape({
  oldPassword: yup
    .string()
    .required('Old password is required')
    .min(1, 'Old password is required'),
  newPassword: yup
    .string()
    .required('New password is required')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    ),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('newPassword')], 'Passwords must match'),
});

// Welcome back password schema
export const welcomeBackSchema = yup.object().shape({
  password: yup
    .string()
    .required('Password is required')
    .min(1, 'Password is required'),
  rememberMe: yup.boolean().default(false),
});

// Email validation helper
export const emailValidation = yup
  .string()
  .email('Please enter a valid email address')
  .required('Email is required');

// Phone validation helper
export const phoneValidation = yup
  .string()
  .test('phone-format', 'Please enter a valid phone number', (value) => {
    if (!value) return false;
    const cleanedPhone = value.replace(/[^\d+]/g, '');
    if (cleanedPhone.startsWith('+')) {
      return /^\+[1-9]\d{6,14}$/.test(cleanedPhone);
    } else {
      return /^[1-9]\d{6,14}$/.test(cleanedPhone);
    }
  })
  .required('Phone number is required');

// Username validation helper
export const usernameValidation = yup
  .string()
  .min(3, 'Username must be at least 3 characters')
  .max(30, 'Username must be less than 30 characters')
  .matches(/^[a-zA-Z0-9._-]+$/, 'Username can only contain letters, numbers, dots, underscores, and hyphens')
  .required('Username is required');

// Initial screen validation schema (for user, email, and phone)
export const initialScreenSchema = yup.object().shape({
  username: yup
    .string()
    .required('Username, email, or phone number is required')
    .test('spelling-validation', 'Please check your spelling and use valid characters', function (value) {
      // If value is empty or undefined, let the required() validation handle it
      if (!value || value.trim() === '') {
        return true; // Let required() handle empty values
      }

      const trimmedValue = value.trim();

      // Check for invalid characters that suggest spelling errors
      // Allow valid email characters: letters, numbers, dots, underscores, hyphens, plus signs, @
      const validEmailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (validEmailPattern.test(trimmedValue)) {
        return true; // Valid email format
      }

      // Check for invalid characters that are definitely wrong
      const definitelyInvalidChars = /[<>()[\]{}|\\:;"'`~!#$%^&*+=?/]/g;
      if (definitelyInvalidChars.test(trimmedValue)) {
        return false;
      }

      // Check for consecutive special characters (like commas, dots, etc.)
      if (/[,.]{2,}/.test(trimmedValue)) {
        return false;
      }

      // For non-email inputs, check if they contain invalid characters
      if (!trimmedValue.includes('@')) {
        // For usernames and phone numbers, check for invalid characters
        const invalidForUsername = /[^a-zA-Z0-9._-]/g;
        if (invalidForUsername.test(trimmedValue)) {
          return false;
        }
      }

      return true;
    })
    .test('valid-input', 'Please enter a valid username, email, or phone number', function (value) {
      // If value is empty or undefined, let the required() validation handle it
      if (!value || value.trim() === '') {
        return true; // Let required() handle empty values
      }

      const trimmedValue = value.trim();

      // Check if it's an email
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (emailRegex.test(trimmedValue)) {
        return true;
      }

      // Check if it's a phone number
      const cleanedPhone = trimmedValue.replace(/[^\d+]/g, '');
      if (cleanedPhone.length >= 7) {
        if (cleanedPhone.startsWith('+')) {
          if (/^\+[1-9]\d{6,14}$/.test(cleanedPhone)) {
            return true;
          }
        } else {
          if (/^[1-9]\d{6,14}$/.test(cleanedPhone)) {
            return true;
          }
        }
      }

      // Check if it's a username
      if (trimmedValue.length >= 3 && trimmedValue.length <= 30 && /^[a-zA-Z0-9._-]+$/.test(trimmedValue)) {
        return true;
      }

      return false;
    }),
  rememberMe: yup.boolean().default(false),
});

// Export all schemas
export const validationSchemas = {
  login: loginSchema,
  forgotPassword: forgotPasswordSchema,
  otp: otpSchema,
  password: passwordSchema,
  setPassword: setPasswordSchema,
  welcomeBack: welcomeBackSchema,
  initialScreen: initialScreenSchema,
  email: emailValidation,
  phone: phoneValidation,
  username: usernameValidation,
};