# Use Python 3.12 as the base image
FROM python:3.12-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /app

# Install system dependencies for mysqlclient
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    gcc \
    default-libmysqlclient-dev \
    pkg-config \
    netcat-traditional \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy project files
COPY . .

# Create wait-for-db script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
host="$1"\n\
shift\n\
cmd="$@"\n\
\n\
until mysql -h "$host" -u root -prootpassword -e "SELECT 1" > /dev/null 2>&1; do\n\
  >&2 echo "MySQL is unavailable - sleeping"\n\
  sleep 1\n\
done\n\
\n\
>&2 echo "MySQL is up - executing command"\n\
exec $cmd' > /app/wait-for-db.sh \
    && chmod +x /app/wait-for-db.sh

# Expose port
EXPOSE 8000

# Run the application with wait-for-db
CMD ["./wait-for-db.sh", "db", "python", "manage.py", "migrate", "--noinput", "&&", "python", "manage.py", "runserver", "0.0.0.0:8000"] 