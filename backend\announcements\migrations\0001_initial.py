# Generated by Django 5.2 on 2025-06-02 15:14

import announcements.models
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('towers', '0002_initial'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Announcement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('description', models.TextField()),
                ('post_as', models.CharField(choices=[('creator', 'Creator'), ('group', 'Group'), ('member', 'Member')], default='creator', max_length=20)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('high', 'High')], max_length=10)),
                ('label', models.Char<PERSON><PERSON>(choices=[('sports', 'Sports'), ('events', 'Events'), ('maintenance', 'Maintenance'), ('general', 'General')], max_length=20)),
                ('start_date', models.DateField()),
                ('start_time', models.TimeField()),
                ('end_date', models.DateField()),
                ('end_time', models.TimeField()),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('upcoming', 'Upcoming'), ('ongoing', 'Ongoing'), ('expired', 'Expired')], default='draft', max_length=10)),
                ('views', models.IntegerField(default=0)),
                ('is_pinned', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='announcement_creator', to='user.member')),
                ('creator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_announcements', to='user.member')),
                ('target_towers', models.ManyToManyField(blank=True, related_name='announcements', to='towers.tower')),
                ('target_units', models.ManyToManyField(blank=True, related_name='announcements', to='towers.unit')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='announcement_updater', to='user.member')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AnnouncementAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(upload_to=announcements.models.upload_to_announcement_attachments, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'])])),
                ('file_name', models.CharField(max_length=255)),
                ('file_type', models.CharField(max_length=50)),
                ('file_size', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('announcement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='announcements.announcement')),
            ],
        ),
        migrations.CreateModel(
            name='AnnouncementHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('edited_at', models.DateTimeField(auto_now_add=True)),
                ('changes', models.JSONField()),
                ('announcement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='announcements.announcement')),
                ('edited_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.member')),
            ],
        ),
    ]
