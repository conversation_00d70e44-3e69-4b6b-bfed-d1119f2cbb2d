# Generated by Django 5.0.4 on 2025-05-25 14:54

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AuditTrail',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('event_type', models.CharField(choices=[('MEMBER_CREATED', 'MEMBER CREATED'), ('MEMBER_UPDATED', 'MEMBER UPDATED'), ('MEMBER_STATUS_CHANGED', 'MEMBER STATUS CHANGED'), ('GROUP_CREATE', 'Group Created'), ('GROUP_UPDATE', 'Group Updated'), ('GROUP_STATUS_CHANGE', 'GROUP STATUS CHANGE'), ('ROLE_CREATED', 'ROLE CREATED'), ('ROLE_UPDATED', 'ROLE UPDATED'), ('ROLE_STATUS_CHANGED', 'ROLE STATUS CHANGED')], max_length=21)),
                ('table_name', models.CharField(max_length=50)),
                ('row_id', models.BigIntegerField()),
                ('old_data', models.J<PERSON>NField(blank=True, null=True)),
                ('new_data', models.JSONField(blank=True, null=True)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
