# Generated by Django 5.0.4 on 2025-05-25 14:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('group_role', '0001_initial'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='group',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_group', to='user.member'),
        ),
        migrations.AddField(
            model_name='group',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_group', to='user.member'),
        ),
        migrations.AddField(
            model_name='groupmembers',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='group_members_created', to='user.member'),
        ),
        migrations.AddField(
            model_name='groupmembers',
            name='group',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='group_role.group'),
        ),
        migrations.AddField(
            model_name='groupmembers',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='user.member'),
        ),
        migrations.AddField(
            model_name='groupmembers',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='group_members_updated', to='user.member'),
        ),
        migrations.AddField(
            model_name='membersrole',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='members_role_created', to='user.member'),
        ),
        migrations.AddField(
            model_name='membersrole',
            name='member',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='user.member'),
        ),
        migrations.AddField(
            model_name='membersrole',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='members_role_updated', to='user.member'),
        ),
        migrations.AddField(
            model_name='role',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='created_role', to='user.member'),
        ),
        migrations.AddField(
            model_name='role',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='updated_role', to='user.member'),
        ),
        migrations.AddField(
            model_name='membersrole',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='group_role.role'),
        ),
        migrations.AddField(
            model_name='rolegroup',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='role_group_created', to='user.member'),
        ),
        migrations.AddField(
            model_name='rolegroup',
            name='group',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='group_role.group'),
        ),
        migrations.AddField(
            model_name='rolegroup',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='group_role.role'),
        ),
        migrations.AddField(
            model_name='rolegroup',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='role_group_updated', to='user.member'),
        ),
        migrations.AddField(
            model_name='rolepermission',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='role_permission_create', to='user.member'),
        ),
        migrations.AddField(
            model_name='rolepermission',
            name='permission',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='permission_roles', to='group_role.permission'),
        ),
        migrations.AddField(
            model_name='rolepermission',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='role_permissions', to='group_role.role'),
        ),
        migrations.AddField(
            model_name='rolepermission',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.DO_NOTHING, related_name='role_permission_update', to='user.member'),
        ),
    ]
