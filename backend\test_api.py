#!/usr/bin/env python
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
django.setup()

from django.contrib.auth.models import User
from user.models import Member, MemberType
from django.core.exceptions import ObjectDoesNotExist

def create_test_user():
    """Create a test user for API testing"""
    try:
        # Check if test user already exists
        user = User.objects.get(username='hasan')
        print(f"Test user 'hasan' already exists with ID: {user.id}")
        return user
    except ObjectDoesNotExist:
        # Create test user
        user = User.objects.create_user(
            username='hasan',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"Created test user 'hasan' with ID: {user.id}")
        
        # Get or create member type
        member_type, created = MemberType.objects.get_or_create(
            type_name='Test Member'
        )
        
        # Create member
        member = Member.objects.create(
            user=user,
            member_type=member_type,
            full_name='Hasan Test User',
            general_contact='1234567890',
            general_email='<EMAIL>',
            login_email='<EMAIL>',
            is_first_login=True
        )
        print(f"Created member for user with ID: {member.id}")
        return user

def test_check_status_api():
    """Test the check_status API endpoint"""
    import requests
    import json
    
    # Test data
    test_data = {
        'authenticator': '<EMAIL>'
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:8000/user/check_status/',
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"User ID: {data.get('user_id')}")
            print(f"Is First Login: {data.get('is_first_login')}")
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the server. Make sure Django is running on port 8000.")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    print("Creating test user...")
    create_test_user()
    
    print("\nTesting check_status API...")
    test_check_status_api() 