# Generated by Django 5.0.4 on 2025-05-25 14:54

import django.core.validators
import towers.models
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Floor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('floor_no', models.IntegerField()),
                ('number_of_units', models.IntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Owner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ownership_percentage', models.DecimalField(decimal_places=2, max_digits=5)),
                ('date_of_ownership', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='OwnerDocs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('owner_docs', models.FileField(blank=True, null=True, upload_to=towers.models.upload_to_owner_photo)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Resident',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('is_resident_or_tenant', models.BooleanField(default=True, help_text='True if resident, False if tenant')),
                ('unit_rent_fee', models.FloatField(default=0)),
                ('advance_payment', models.FloatField(default=0)),
                ('notice_period', models.IntegerField(help_text='Notice period in months')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ResidentDocs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rental_docs', models.FileField(null=True, upload_to=towers.models.upload_to_rental_docs, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png'])])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Tower',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('tower_name', models.CharField(max_length=255)),
                ('tower_number', models.IntegerField(null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('photo', models.ImageField(null=True, upload_to='towers/', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])),
                ('num_floors', models.IntegerField(default=0)),
                ('num_units', models.IntegerField(default=0)),
                ('unit_naming_type', models.CharField(max_length=255)),
                ('add_tower_number_to_unit_name', models.BooleanField(default=0)),
                ('units_per_floor', models.CharField(default='Same as Every Floor', max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Unit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_name', models.CharField(max_length=20)),
                ('unit_status', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True, null=True)),
                ('area', models.IntegerField(null=True)),
                ('number_of_rooms', models.IntegerField(null=True)),
                ('number_of_bathrooms', models.IntegerField(null=True)),
                ('number_of_balconies', models.IntegerField(null=True)),
                ('primary_name', models.CharField(max_length=255, null=True)),
                ('primary_number', models.CharField(max_length=11, null=True)),
                ('primary_email', models.EmailField(max_length=254, null=True)),
                ('primary_relationship', models.CharField(max_length=50, null=True)),
                ('secondary_name', models.CharField(max_length=255, null=True)),
                ('secondary_number', models.CharField(max_length=11, null=True)),
                ('secondary_email', models.EmailField(max_length=254, null=True)),
                ('secondary_relationship', models.CharField(max_length=50, null=True)),
                ('emergency_name', models.CharField(max_length=255, null=True)),
                ('emergency_number', models.CharField(max_length=11, null=True)),
                ('emergency_email', models.EmailField(max_length=254, null=True)),
                ('emergency_relationship', models.CharField(max_length=50, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UnitDocs',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('unit_docs', models.FileField(null=True, upload_to=towers.models.upload_to_unit_docs, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'jpg', 'jpeg', 'png'])])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='UnitStaff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('unit_staff_status', models.BooleanField(default=True, help_text='True if Live-in, False if Part-time')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
