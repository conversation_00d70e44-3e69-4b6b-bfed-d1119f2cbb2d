# Generated by Django 5.1.4 on 2025-06-18 11:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('towers', '0004_owner_ownership_transfer_from'),
    ]

    operations = [
        migrations.AddField(
            model_name='unit',
            name='status_color',
            field=models.CharField(default='#FFFFFF', help_text='Hex color code for unit status', max_length=7),
        ),
        migrations.AlterField(
            model_name='unit',
            name='unit_status',
            field=models.CharField(choices=[('no_owner', 'No Owner'), ('available', 'Available'), ('occupied', 'Occupied'), ('unknown', 'Unknown')], default='no_owner', max_length=20),
        ),
    ]
