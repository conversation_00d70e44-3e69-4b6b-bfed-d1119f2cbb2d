from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q
from rest_framework.response import Response
from rest_framework import status
from user.models import Member
from user.serializers import MemberSerializer
from towers.serializers.owner_serializers import MemberUnitOwnershipSerializer, OwnerSerializer,OwnerDetailSerializer
from towers.models import Owner, OwnerDocs
from towers.serializers.tower_serializers import UnitSerializer
from towers.models import Unit, Resident, UnitStaff
from django.forms.models import model_to_dict
from audit_trail.create_audit_trail import create_audit_trail
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.db import transaction

class AddOwnerSearch(APIView):
    def get(self, request):
        search = request.GET.get("search", "").strip()
        unit_id = request.GET.get("unit_id")

        print(f"🔍 DEBUG: Search term: '{search}', Unit ID: {unit_id}")

        if len(search) < 3:
            print(f"🔍 DEBUG: Search term too short ({len(search)} chars), returning empty")
            return Response({"member_data": []}, status=status.HTTP_200_OK)

        member_data = []

        # --- OWNERS ---
        owners = Owner.objects.select_related("member", "unit__floor__tower")
        if unit_id:
            owners = owners.filter(unit_id=unit_id)
        owners = owners.filter(
            Q(member__full_name__icontains=search)
        )
        print(f"🔍 DEBUG: Found {owners.count()} owners")
        for owner in owners:
            member_data.append({
                "id": owner.member.id,
                "full_name": owner.member.full_name,
                "tower": owner.unit.floor.tower.tower_name if owner.unit.floor and owner.unit.floor.tower else None,
                "unit": owner.unit.unit_name,
                "roles": ["Owner"]
            })

        # --- RESIDENTS ---
        residents = Resident.objects.filter(is_active=True).select_related("member", "unit__floor__tower")
        if unit_id:
            residents = residents.filter(unit_id=unit_id)
        residents = residents.filter(
            Q(member__full_name__icontains=search)
        )
        print(f"🔍 DEBUG: Found {residents.count()} residents")
        for resident in residents:
            member_data.append({
                "id": resident.member.id,
                "full_name": resident.member.full_name,
                "tower": resident.unit.floor.tower.tower_name if resident.unit.floor and resident.unit.floor.tower else None,
                "unit": resident.unit.unit_name,
                "roles": ["Resident"]
            })

        # --- UNIT STAFF ---
        unit_staff = UnitStaff.objects.filter(is_active=True).select_related("member", "unit__floor__tower")
        if unit_id:
            unit_staff = unit_staff.filter(unit_id=unit_id)
        unit_staff = unit_staff.filter(
            Q(member__full_name__icontains=search)
        )
        print(f"🔍 DEBUG: Found {unit_staff.count()} unit staff")
        for staff in unit_staff:
            member_data.append({
                "id": staff.member.id,
                "full_name": staff.member.full_name,
                "tower": staff.unit.floor.tower.tower_name if staff.unit.floor and staff.unit.floor.tower else None,
                "unit": staff.unit.unit_name,
                "roles": ["Unit Staff"]
            })

        # --- COMPANY MEMBERS ---
        from user.models import Company
        company_members = Company.objects.filter(company_name__icontains=search).select_related("member")
        print(f"🔍 DEBUG: Found {company_members.count()} company members")
        for company in company_members:
            if company.member and search.lower() in company.member.full_name.lower():
                member_data.append({
                    "id": company.member.id,
                    "full_name": company.member.full_name,
                    "tower": None,
                    "unit": None,
                    "roles": ["Company"]
                })
            elif search.lower() in company.company_name.lower():
                member_data.append({
                    "id": f"company-{company.id}",
                    "full_name": company.company_name,
                    "tower": None,
                    "unit": None,
                    "roles": ["Company"]
                })

        # --- ORG MEMBERS ---
        # Don't exclude org members even if they are already owners/residents/unit staff
        # They should appear in both categories
        org_members = Member.objects.filter(
            is_org_member=True
        ).filter(
            Q(full_name__icontains=search)
        )
        
        # Debug: Log org members found
        print(f"🔍 DEBUG: Found {org_members.count()} org members for search: '{search}'")
        
        # Debug: Show all org members with their details
        all_org_members = Member.objects.filter(is_org_member=True)
        print(f"🔍 DEBUG: All org members in database:")
        for member in all_org_members:
            print(f"  - {member.full_name} (ID: {member.id}, is_org_member: {member.is_org_member})")
        
        for member in org_members:
            print(f"🔍 DEBUG: Adding org member: {member.full_name} (ID: {member.id})")
            member_data.append({
                "id": member.id,
                "full_name": member.full_name,
                "tower": None,
                "unit": None,
                "roles": ["Management"]
            })

        # Debug: Log final response
        print(f"🔍 DEBUG: Final member_data count: {len(member_data)}")
        print(f"🔍 DEBUG: Final member_data: {member_data}")

        return Response({"member_data": member_data}, status=status.HTTP_200_OK)

class CreateOwner(APIView):
    # permission_classes = [IsAuthenticated,HasRequiredPermission]
    # required_permission_id = [11]
    def post(self, request):
        member_id = request.data.get('member') 
        unit = request.data.get('unit') 

        if member_id and Owner.objects.filter(member=member_id,unit=unit).exists():
            return Response(
                {"error": "This member is already associated with an owner."},
                status=status.HTTP_400_BAD_REQUEST
            )
        serializer = OwnerSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            owner = serializer.save()
            owner_data = model_to_dict(owner)
            # create_audit_trail(
            #     member=request.user,  # or pass in owner if needed
            #     event_type='OWNER_CREATED',
            #     table_name='Owner',
            #     row_id=owner.id,
            #     old_data=None,
            #     new_data=owner_data,
            #     description=f'New owner created successfully.'
            # )
            return Response( 
                {"message": "Owner created successfully", "data": serializer.data}, 
                status=status.HTTP_201_CREATED
            )
            
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)  

class UpdateOwner(APIView):
    # permission_classes = [IsAuthenticated]
    def put(self, request, owner_id):
        print("🛬 Incoming Update Owner request data:")
        print(dict(request.data))  # Print all incoming form data

        owner = get_object_or_404(Owner, id=owner_id)
        serializer = OwnerSerializer(owner, data=request.data, context={'request': request}, partial=True)
        
        if serializer.is_valid():
            updated_owner = serializer.save()
            print("✅ Owner updated successfully:", model_to_dict(updated_owner))
            return Response({
                "message": "Owner updated successfully.",
                "data": OwnerSerializer(updated_owner, context={'request': request}).data
            }, status=status.HTTP_200_OK)
        
        print("❌ Validation errors:", serializer.errors)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class DeleteOwner(APIView):
    def delete(self, request, owner_id):
        owner = get_object_or_404(Owner, id=owner_id)

        with transaction.atomic():
            # Get all related owner docs first
            owner_docs = OwnerDocs.objects.filter(owner=owner)
            
            # Delete the actual files from media storage first
            for doc in owner_docs:
                if doc.owner_docs:
                    try:
                        # Delete the file from storage
                        doc.owner_docs.delete(save=False)
                        print(f"🗑️ Deleted file: {doc.owner_docs.name}")
                    except Exception as e:
                        print(f"⚠️ Error deleting file {doc.owner_docs.name}: {e}")
            
            # Then delete the database records
            owner_docs.delete()

            # Finally delete the owner
            owner.delete()

        return Response(
            {"message": "Owner and related documents deleted successfully."},
            status=status.HTTP_200_OK
        )

    
class OwnerListOfUnit(APIView):
    def get(self, request, unit_id):
        # Get the unit or return 404 if not found
        unit = get_object_or_404(Unit, id=unit_id)

        # Get all owners of this unit, ordered by created_at timestamp
        # Only include owners whose members are active community members
        owners = Owner.objects.filter(
            unit=unit,
            member__is_comm_member=True  # Only include active community members
        ).select_related('member').order_by('created_at')

        # Serialize the data with request context
        serializer = OwnerDetailSerializer(owners, many=True, context={'request': request})

        return Response({
            'unit': UnitSerializer(unit).data,
            'owners': serializer.data
        }, status=status.HTTP_200_OK)

class OwnerDetails(APIView):
    # permission_classes = [IsAuthenticated]
    def get(self, request, unit_id, owner_id):
        
        try:
            # Filter using the owner's primary key and its associated unit
            owner = Owner.objects.get(id=owner_id, unit__id=unit_id)
        
        except Owner.DoesNotExist:
            return Response(
                {"error": "Owner not found for the specified unit and owner id."},
                status=status.HTTP_404_NOT_FOUND
            )

        # serializer = OwnerSerializer(owner)
        serializer = OwnerDetailSerializer(owner, context={'request': request})  # ✅ use the detailed version with request context

        return Response(serializer.data, status=status.HTTP_200_OK)

class MemberUnitOwnership(APIView):
    def get(self, request, member_id):
        owners = Owner.objects.filter(member__id=member_id).select_related(
            'unit__floor__tower'
        )
        serializer = MemberUnitOwnershipSerializer(owners, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

# class DeleteOwnersByUnit(APIView):
#     def delete(self, request, unit_id):
#         try:
#             # Verify unit exists
#             unit = Unit.objects.get(id=unit_id)
            
#             # Delete all owners related to this unit
#             deleted_count, _ = Owner.objects.filter(unit=unit).delete()

#             return Response(
#                 {"message": f"Successfully deleted {deleted_count} owner(s) associated with unit {unit.unit_name}."},
#                 status=status.HTTP_200_OK
#             )
#         except Unit.DoesNotExist:
#             return Response(
#                 {"error": "Unit not found."},
#                 status=status.HTTP_404_NOT_FOUND
#             )
