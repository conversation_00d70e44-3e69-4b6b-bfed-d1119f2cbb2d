
from towers.serializers.unitStaff_serializers import UnitStaffSerializer,UnitStaffMemberSerializer
from towers.models import <PERSON><PERSON><PERSON><PERSON>,Resident,Owner
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound
from user.permissions import HasRequiredPermission
from django.shortcuts import get_object_or_404,get_list_or_404
from rest_framework.views import APIView
from rest_framework.response import Response
from user.models import Member
from audit_trail.create_audit_trail import create_audit_trail
from django.forms.models import model_to_dict
from django.core.mail import send_mail
from django.db import transaction
from django.conf import settings
from django.db.models import Q
from group_role.models import MembersRole,GroupMembers


import logging


logger = logging.getLogger(__name__)



class CreateUnitStaff(APIView):
    # permission_classes = [IsAuthenticated, HasRequiredPermission]
    # required_permission_id = [11]
    def post(self, request):
        serializer = UnitStaffSerializer(data=request.data, context={'request': request})
        if serializer.is_valid():
            unit_staff = serializer.save()
            group_data = model_to_dict(unit_staff)
            try:
                member = request.user.member
            except AttributeError:
              
                member = Member.objects.get(user=request.user)
            
            
            create_audit_trail(
                member=member,
                event_type='UnitStaff_CREATE',
                table_name='UnitStaff',
                row_id=unit_staff.id,
                new_data=group_data,
                description='UnitStaff Created'
            )
            return Response(
                {"message": "UnitStaff created successfully", "data": serializer.data},
                status=status.HTTP_201_CREATED
            )
       
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
class UnitStaffMemberList(APIView):
    # permission_classes = [IsAuthenticated]

    def get(self, request, unit_pk):

        unit_staff = UnitStaff.objects.select_related('member', 'unit').filter(
            unit__id=unit_pk,
            is_active=True,
            member__is_comm_member=True  # Only include active community members
        )
        serializer = UnitStaffMemberSerializer(unit_staff, many=True)

        return Response(serializer.data)

# Created by Firoj Hasan
# Description:
# - UpdateUnitStaffStatus: Update a single UnitStaff's unit_staff_status field.
# - BulkDeactivateUnitStaff: Soft delete .
# ================================================

class UpdateUnitStaffStatus(APIView):
    # permission_classes = [IsAuthenticated]

    def patch(self, request, pk):
        unit_staff = get_object_or_404(UnitStaff, pk=pk)

        new_status = request.data.get('unit_staff_status')

        if new_status is None:
            return Response({"error": "staff status is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            member = request.user.member
        except AttributeError:
            member = Member.objects.get(user=request.user)

        old_data = {"unit_staff_status": unit_staff.unit_staff_status}
        unit_staff.unit_staff_status = new_status
        unit_staff.updated_by = member
        unit_staff.save()

        new_data = {"unit_staff_status": unit_staff.unit_staff_status}

        create_audit_trail(
            member=member,
            event_type='STAFF_STATUS_UPD',
            table_name='UnitStaff',
            row_id=unit_staff.id,
            old_data=old_data,
            new_data=new_data,
            description='Updated unit_staff_status field only'
        )

        return Response({
            "message": "Unit Staff Status Updated Successfully",
            "data": {
                "id": unit_staff.id,
                "unit_staff_status": unit_staff.unit_staff_status
            }
        }, status=status.HTTP_200_OK)
    


    # ================================================

class BulkDeleteUnitStaff(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, *args, **kwargs):
        """
        Bulk delete UnitStaff records.
        Expects in request.data:
        - ids (list[int]): list of UnitStaff IDs to delete
        """
        ids = request.data.get('ids')

        # Validate payload
        if not isinstance(ids, list) or not ids:
            return Response(
                {"error": "A non‑empty list of 'ids' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            actor = request.user.member
        except Member.DoesNotExist:
            return Response(
                {"error": "Requesting user is not linked to a Member."},
                status=status.HTTP_400_BAD_REQUEST
            )

        unit_staff_qs = get_list_or_404(UnitStaff, id__in=ids)
        affected_members = set()

        with transaction.atomic():
            for staff in unit_staff_qs:
                affected_members.add(staff.member.id)

                # Save audit data before deletion
                create_audit_trail(
                    member=actor,
                    event_type='STAFF_DELETED',
                    table_name='UnitStaff',
                    row_id=staff.id,
                    old_data={"is_active": staff.is_active},
                    new_data={"deleted": True},
                    description=f"UnitStaff id={staff.id} deleted."
                )

                # Delete the staff entry
                staff.delete()

            # Check and update each affected member
            for member_id in affected_members:
                still_exists = (
                    Resident.objects.filter(member_id=member_id, is_active=True).exists() or
                    UnitStaff.objects.filter(member_id=member_id, is_active=True).exists() or
                    Owner.objects.filter(member_id=member_id).exists()
                )
                if not still_exists:
                    # Member.objects.filter(id=member_id).update(is_comm_member=False)
                    MembersRole.objects.filter(member__id=member_id).delete()
                    # remove any GroupMembers rows that would block deletion
                    GroupMembers.objects.filter(member_id=member_id).delete()
                    Member.objects.filter(id=member_id).delete()


        return Response(
            {"message": f"{len(unit_staff_qs)} UnitStaff deleted successfully."},
            status=status.HTTP_200_OK
        )


# class BulkDeactivateUnitStaff(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, *args, **kwargs):
        """
        Bulk soft delete or activate UnitStaff records.
        Expects in request.data:
        - ids (list[int]): list of UnitStaff IDs
        - is_active (bool): True to activate, False to deactivate
        """
        ids = request.data.get('ids')
        is_active = request.data.get('is_active')

        # Validate payload
        if not isinstance(ids, list) or not ids:
            return Response(
                {"error": "A non‑empty list of 'ids' is required."},
                status=status.HTTP_400_BAD_REQUEST
            )
        if not isinstance(is_active, bool):
            return Response(
                {"error": "'is_active' field (boolean) is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Ensure the requesting user has a Member
        try:
            actor = request.user.member
        except Member.DoesNotExist:
            return Response(
                {"error": "Requesting user is not linked to a Member."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Fetch UnitStaff entries (404 if any ID is invalid)
        unit_staff_qs = get_list_or_404(UnitStaff, id__in=ids)
        updated_records = []

        # Wrap in a transaction
        with transaction.atomic():
            for staff in unit_staff_qs:
                old_data = {"is_active": staff.is_active}

                # Only proceed if status is actually changing
                if staff.is_active != is_active:
                    staff.is_active = is_active
                    staff.updated_by = actor
                    staff.save(update_fields=['is_active', 'updated_by', 'updated_at'])

                    new_data = {"is_active": staff.is_active}
                    updated_records.append({
                        "id": staff.id,
                        "is_active": staff.is_active
                    })

                    # Send email notification
                    recipient_email = getattr(staff.member, 'general_email', None)
                    if recipient_email:
                        subject = (
                            "Account Activated"
                            if is_active
                            else "Account Deactivated"
                        )
                        message = (
                            "Your account has been activated."
                            if is_active
                            else "Your account has been deactivated."
                        )
                        try:
                            send_mail(
                                subject,
                                message,
                                settings.EMAIL_HOST_USER,
                                [recipient_email],
                                fail_silently=False
                            )
                        except Exception as e:
                            logger.error(
                                f"Failed to send email to UnitStaff id={staff.id} "
                                f"({recipient_email}): {e}"
                            )
                    else:
                        logger.warning(f"No general_email found for Member {staff.member.id}")

                    # Record the change in audit trail
                    create_audit_trail(
                        member=actor,
                        event_type='STAFF_STATUS_CHANGE',
                        table_name='UnitStaff',
                        row_id=staff.id,
                        old_data=old_data,
                        new_data=new_data,
                        description=f"Bulk set is_active={is_active}"
                    )

        return Response(
            {
                "message": f"{len(updated_records)} UnitStaff removed successfully.",
                "data": updated_records
            },
            status=status.HTTP_200_OK
        )