from django.db import models
from django.contrib.auth.models import User
from django.core.validators import FileExtensionValidator
import os



class MemberType(models.Model):
    type_name = models.CharField(max_length=128)

def upload_to_member_photo(instance, filename):
    # Generate a custom path for the image based on the instance's ID
    return os.path.join('members', f'{instance.id}_{instance.full_name}', filename)
# Create your models here.
class Member(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE,null=True)
    member_type = models.ForeignKey(MemberType,on_delete=models.CASCADE,null=False,default = 1)
    # member_type = models.ForeignKey(MemberType, on_delete=models.CASCADE, null=True, blank=True)

    full_name = models.CharField(max_length=255)
    general_contact = models.Char<PERSON>ield(max_length=11,unique=False,null=False)
    general_email = models.EmailField(unique=False, null=False,default = '<EMAIL>')
    login_email = models.EmailField(unique=True, null=True)
    login_contact = models.CharField(max_length=11, unique=True, null=True)
    nid_number = models.CharField(max_length=255, null=True,unique=True)
    photo = models.ImageField(upload_to=upload_to_member_photo,null=True,validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])
    photo_low_quality = models.ImageField(upload_to=upload_to_member_photo,null=True)
    about_us = models.TextField(null=True)
    facebook_profile = models.TextField(null=True)
    linkedin_profile = models.TextField(null=True)
    permanent_address = models.TextField(null=True)
    present_address = models.TextField(null=True)
    date_of_birth = models.DateField(null=True)
    occupation = models.CharField(max_length=255, null=True)
    gender = models.CharField(max_length=10, null=True)
    marital_status = models.CharField(max_length=10, null=True)
    religion = models.CharField(max_length=20, null=True)
    nid_front = models.ImageField(upload_to=upload_to_member_photo,null=True,validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])
    nid_back = models.ImageField(upload_to=upload_to_member_photo,null=True,validators=[FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png', 'heic'])])
    is_org_member = models.BooleanField(default=0)
    is_comm_member = models.BooleanField(default=0)
    org_member_ever_created = models.BooleanField(default=0)
    comm_member_ever_created = models.BooleanField(default=0)
    is_first_login = models.BooleanField(default=True)
    created_by = models.ForeignKey('self', null=True, on_delete=models.DO_NOTHING, related_name='members_created')
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_by = models.ForeignKey('self', null=True, on_delete=models.DO_NOTHING, related_name='members_updated')
    updated_at = models.DateTimeField(auto_now=True, null=True)
    


    def __str__(self):
        return self.user.username

    
    def get_permission_ids(self):
        """
        Traverse from the member to roles, then to role permissions,
        and finally gather the permission IDs.
        """
        from group_role.models import Permission,MembersRole,Role,RolePermission
        # Get roles for this member
        members_roles = MembersRole.objects.filter(member_id=self.pk)
        # print(members_roles)
        roles = Role.objects.filter(id__in=members_roles.values_list('role_id', flat=True),is_active = True)
        # print(roles)
        role_permissions = RolePermission.objects.filter(role_id__in=roles.values_list('id', flat=True),is_active = True)
        # print(role_permissions)
        permissions = Permission.objects.filter(id__in=role_permissions.values_list('permission_id', flat=True))
        # print(permissions)
        # Now query Permission IDs using the join via RolePermission
        permission_ids = permissions.values_list('id', flat=True)
        # print(permission_ids)
        return set(permission_ids)
    
    def save(self, *args, **kwargs):
        # Update ever created flags if current status is True
        # if self.is_org_member:
        #     self.org_member_ever_created = True
        # if self.is_comm_member:
        #     self.comm_member_ever_created = True

        # Ensure that the directory exists before saving
        if self.photo or self.nid_back or self.nid_front:
            # Get the directory where the image will be stored
            media_directory = os.path.join('media', 'members',f"{self.id}_{self.full_name}")

            # Create the directory if it doesn't exist
            if not os.path.exists(media_directory):
                os.makedirs(media_directory)    
        super(Member, self).save(*args, **kwargs)
    

class MyModel(models.Model):
    # First field: A CharField to store a name
    name = models.CharField(max_length=100)
    # Second field: An IntegerField to store an age or some numeric value
    age = models.IntegerField()
    def __str__(self):
        return f'{self.name}, {self.age}'
    
class Company(models.Model):
    from towers.models import Unit

    company_name = models.CharField(
    max_length=255,
    unique=True,
    error_messages={
        'unique': "This company name already exists.",
        'blank': "Company name cannot be blank.",
        'null': "Company name cannot be null.",
        }
         )

    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name="companies", null=True, blank=True)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE, related_name="companies", null=True, blank=True)
    created_by = models.ForeignKey(
        Member, null=True, blank=True, on_delete=models.DO_NOTHING, related_name='company_created'
    )
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_by = models.ForeignKey(Member, null=True, blank=True, on_delete=models.DO_NOTHING, related_name='company_updated')
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.company_name
