import logging
from rest_framework.permissions import BasePermission
from group_role.models import Member

logger = logging.getLogger(__name__)

class HasRequiredPermission(BasePermission):
    def has_permission(self, request, view):
        try:
            member = request.user.member
        except Exception:
            member = Member.objects.filter(user=request.user).first()

        if not member:
            # logger.warning(f"User {request.user} is not linked to a member.")
            return False

        # Retrieve required permissions from the view
        required_permissions = getattr(view, 'required_permission_id', [])
        if not isinstance(required_permissions, list):
            required_permissions = [required_permissions]

        # If no required permissions are specified, allow access.
        if not required_permissions:
            return True

        # Get the user's permission IDs.
        user_permission_ids = member.get_permission_ids()

        try:
            user_permission_ids = set(map(int, user_permission_ids))
            required_permissions = list(map(int, required_permissions))
        except Exception as e:
            # logger.error(f"Error converting permission types for user {request.user}: {e}")
            return False

        # logger.debug(f"User {request.user} has permissions {user_permission_ids}. Required: {required_permissions}")

        # Check if at least one required permission is in the user's permissions.
        has_access = any(permission in user_permission_ids for permission in required_permissions)
        # if has_access:
        #     logger.info(f"Access granted for user {request.user}.")
        # else:
        #     logger.info(f"Access denied for user {request.user}. Missing required permissions: {required_permissions}")
        return has_access
